const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const bundleObfuscator = require('./bundle-obfuscator');
const exclusionList = require('metro-config/src/defaults/exclusionList');
const path = require('path');

/**
 * Metro configuration
 * https://facebook.github.io/metro/docs/configuration
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = (() => {
  const {
    resolver: { sourceExts, assetExts },
  } = getDefaultConfig();
  return {
    resetCache: true,
    transformer: {
      babelTransformerPath: require.resolve('react-native-svg-transformer'),
      getTransformOptions: async () => ({
        transform: {
          experimentalImportSupport: false,
          inlineRequires: true,
        },
      }),
    },
    resolver: {
      assetExts: assetExts.filter(ext => ext !== 'svg'),
      blockList: exclusionList([
        // Ignore pulse mobile sdk react & react-native installations
        /^.*@bp\/pulse-mobile-sdk\/node_modules\/react[/\\].*/,
        /^.*@bp\/pulse-mobile-sdk\/node_modules\/react-native[/\\].*/,
        // Ignore react & react-native installations in MFEs
        // These should be turned into peerDependencies in the MFEs
        /^.*mfes\/.*\/node_modules\/react[/\\].*/,
        /^.*mfes\/.*\/node_modules\/react-native[/\\].*/,
      ]),
      sourceExts: [...sourceExts, 'svg'],
      unstable_enableSymlinks: true,
      nodeModulesPaths: [
        path.resolve(
          __dirname,
          '../node_modules/@bp/pulse-mobile-sdk/node_modules',
        ),
      ],
    },
    projectRoot: path.resolve(__dirname),
    watchFolders: [
      path.resolve(__dirname, '../node_modules'),
      path.resolve(__dirname, '../mfes/credit'),
      path.resolve(__dirname, '../mfes/offers'),
      path.resolve(__dirname, '../mfes/registration'),
      // Required to resolve image assets for Android debug
      path.resolve(__dirname, '..'),
    ],
    ...bundleObfuscator,
  };
})();

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
